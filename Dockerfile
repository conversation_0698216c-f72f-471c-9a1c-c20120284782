FROM 10.1.160.100:9092/poetry-ci:latest

# 安装系统工具（用于平滑重启）
RUN apt-get update && apt-get install -y --no-install-recommends \
    procps \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 拷贝项目文件
COPY pyproject.toml poetry.lock ./
COPY app ./app

# 安装项目依赖（不含 dev）
RUN poetry install --only main --no-interaction --no-ansi --no-root

# 暴露端口
EXPOSE 30101

# 拷贝Gunicorn配置
COPY gunicorn.conf.py ./

# 启动服务
CMD ["poetry", "run", "gunicorn", "app.main:app", "-c", "gunicorn.conf.py"]