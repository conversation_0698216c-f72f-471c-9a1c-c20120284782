.PHONY: install format test clean run run-prod restart status health help

# 开发环境
install:
	poetry install

format:
	poetry run black .
	poetry run isort .

test:
	poetry run pytest

clean:
	find . -type d -name "__pycache__" -exec rm -r {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -r {} +
	find . -type d -name "*.egg" -exec rm -r {} +
	find . -type d -name ".pytest_cache" -exec rm -r {} +
	find . -type d -name ".coverage" -exec rm -r {} +

# 运行服务
run:
	poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 30101

run-prod:
	poetry run gunicorn app.main:app -c gunicorn.conf.py

# 生产环境管理
restart:
	@echo "🚀 执行平滑重启..."
	@chmod +x scripts/restart.sh
	@./scripts/restart.sh

status:
	@echo "📊 检查服务状态..."
	@chmod +x scripts/monitor.sh
	@./scripts/monitor.sh --status

health:
	@echo "🔍 执行健康检查..."
	@curl -f -s http://localhost:30101/health | python3 -m json.tool || echo "❌ 健康检查失败"

# 帮助信息
help:
	@echo "📖 可用命令:"
	@echo ""
	@echo "开发环境:"
	@echo "  make install   - 安装依赖"
	@echo "  make run       - 启动开发服务器"
	@echo "  make run-prod  - 启动生产服务器"
	@echo "  make format    - 格式化代码"
	@echo "  make test      - 运行测试"
	@echo "  make clean     - 清理缓存"
	@echo ""
	@echo "生产环境:"
	@echo "  make restart   - 平滑重启服务 ⭐"
	@echo "  make status    - 检查服务状态"
	@echo "  make health    - 健康检查"
	@echo ""
	@echo "  make help      - 显示此帮助信息"