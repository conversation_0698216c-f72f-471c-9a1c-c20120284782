# AI Server

基于FastAPI的AI服务后端项目。

## 功能特性

- FastAPI框架
- Poetry依赖管理
- 自动化测试
- 日志中间件
- CI/CD支持
- **零停机部署** - 基于Gunicorn的平滑重启

## 环境要求

- Python 3.10+
- Poetry

## 安装

1. 克隆项目
```bash
git clone [项目地址]
cd ai_server
```

2. 安装依赖
```bash
make install
```

## 使用

### 开发环境
```bash
# 安装依赖
make install

# 启动开发服务器
make run

# 启动生产服务器（本地测试）
make run-prod

# 格式化代码
make format

# 运行测试
make test

# 清理缓存
make clean
```

### 生产环境
```bash
# 平滑重启服务
make restart

# 检查服务状态
make status

# 监控服务
make monitor

# 查看监控日志
make logs

# 健康检查
make health
```

### Docker操作
```bash
# 构建Docker镜像
make docker-build

# 运行Docker容器
make docker-run

# 停止Docker容器
make docker-stop
```

### 帮助信息
```bash
# 查看所有可用命令
make help
```

## 工作流配置管理

本项目使用YAML配置文件进行动态工作流管理，可以灵活添加和管理多个Dify工作流，完全移除了对环境变量的依赖。

**特性**:
- ✅ 纯配置文件管理，无需环境变量
- ✅ 支持工作流key和唯一标识符两种查询方式
- ✅ 动态启用/禁用工作流
- ✅ 灵活的超时和重试配置

1. **创建配置文件**
```bash
# 复制示例配置文件
cp config/workflows.example.yaml config/workflows.yaml

# 编辑配置文件
vim config/workflows.yaml
```

2. **配置工作流**
```yaml
dify:
  api_url: "https://api.dify.ai"  # 你的Dify API地址
  user: "admin"
  default_timeout: 300
  default_max_retries: 3

workflows:
  # 产线计划工作流
  sop_type1:
    name: "产线计划工作流"
    description: "处理产线计划相关的SOP文档"
    api_key: "your-sop-type1-api-key-here"  # 替换为实际的API密钥
    enabled: true
    timeout: 300
    max_retries: 3
    identifier: "sop-production-planning"

  # 控制计划工作流
  sop_type2:
    name: "控制计划工作流"
    description: "处理控制计划相关的SOP文档"
    api_key: "your-sop-type2-api-key-here"  # 替换为实际的API密钥
    enabled: true
    timeout: 300
    max_retries: 3
    identifier: "sop-control-planning"
```

3. **在代码中使用**
```python
from app.dependencies.common import get_dify_service_by_type
from app.enums.workflow_types import WorkflowType

# 获取工作流服务
service = get_dify_service_by_type("my_workflow")

# 管理工作流类型
all_types = WorkflowType.get_all_types()
enabled_types = WorkflowType.get_enabled_types()

# 根据唯一标识符获取工作流
workflow_type = WorkflowType.get_workflow_by_identifier("sop-production-planning")

# 根据工作流key获取工作流
workflow_type = WorkflowType.get_workflow_by_key("sop_type1")
```

### 配置字段说明

- **identifier**: 工作流的唯一标识符，用于精确查找工作流
- **enabled**: 是否启用该工作流
- **timeout**: 工作流执行超时时间（秒）
- **max_retries**: 最大重试次数
- **api_key**: 工作流的Dify API密钥

### 部署配置

生产环境部署时，需要确保配置文件正确挂载：

```bash
# 确保配置目录存在
mkdir -p /data/server/config

# 复制并编辑配置文件
cp config/workflows.example.yaml /data/server/config/workflows.yaml
vim /data/server/config/workflows.yaml
```

详细配置说明请参考：[工作流配置管理指南](docs/workflow_configuration.md)

## 开发指南

### 项目结构
```
ai_server/
├── app/
│   ├── api/        # API路由
│   ├── core/       # 核心配置
│   ├── middleware/ # 中间件
│   └── utils/      # 工具函数
├── tests/          # 测试文件
├── Makefile        # 项目命令
└── pyproject.toml  # 项目配置
```

### 开发流程

1. 创建新分支
2. 开发新功能
3. 运行测试
4. 提交代码
5. 创建合并请求

## API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
