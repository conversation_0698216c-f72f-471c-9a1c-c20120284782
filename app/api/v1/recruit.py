from typing import Any, Dict

from fastapi import APIRouter, File, UploadFile

from app.dependencies.common import get_dify_service_by_type
from app.services.recruit_service import RecruitService

router = APIRouter()


@router.post("/upload-jd", summary="上传JD文件")
async def upload_jd_file(file: UploadFile = File(...)) -> Dict[str, Any]:
    """根据jd生成岗位画像"""
    dify_flow = get_dify_service_by_type("recruit_jd")
    recruit_service = RecruitService(dify_flow)
    return await recruit_service.process_upload(file)
