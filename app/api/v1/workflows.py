from typing import Any, Dict, List

from fastapi import APIRouter, HTTPException, Query

from app.dependencies.common import get_workflow_info, list_available_workflows
from app.enums.workflow_types import WorkflowType

router = APIRouter()


@router.get("/", summary="获取所有工作流列表")
async def list_workflows(
    enabled_only: bool = Query(True, description="是否只返回启用的工作流")
) -> Dict[str, Any]:
    """获取所有可用的工作流列表"""
    return list_available_workflows(enabled_only)


@router.get("/types", summary="获取工作流类型列表")
async def get_workflow_types(
    enabled_only: bool = Query(True, description="是否只返回启用的工作流类型")
) -> Dict[str, Any]:
    """获取所有工作流类型列表"""
    if enabled_only:
        types = WorkflowType.get_enabled_types()
    else:
        types = WorkflowType.get_all_types()

    return {"total": len(types), "types": types}


@router.get("/identifier/{identifier}", summary="根据唯一标识符获取工作流")
async def get_workflow_by_identifier(identifier: str) -> Dict[str, Any]:
    """根据唯一标识符获取工作流信息"""
    try:
        workflow_type = WorkflowType.get_workflow_by_identifier(identifier)
        return get_workflow_info(workflow_type)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/{workflow_type}", summary="获取指定工作流信息")
async def get_workflow_detail(workflow_type: str) -> Dict[str, Any]:
    """获取指定工作流的详细信息"""
    return get_workflow_info(workflow_type)


@router.post("/{workflow_type}/validate", summary="验证工作流配置")
async def validate_workflow(workflow_type: str) -> Dict[str, Any]:
    """验证指定工作流的配置是否正确"""
    try:
        # 尝试创建工作流服务实例来验证配置
        from app.dependencies.common import get_dify_service_by_type

        dify_service = get_dify_service_by_type(workflow_type)

        return {
            "workflow_type": workflow_type,
            "valid": True,
            "message": "工作流配置验证成功",
            "config": {
                "api_url": dify_service.base_url,
                "user": dify_service.user,
                "timeout": dify_service.timeout,
                "max_retries": dify_service.max_retries,
            },
        }
    except HTTPException as e:
        return {
            "workflow_type": workflow_type,
            "valid": False,
            "message": e.detail,
            "config": None,
        }
    except Exception as e:
        return {
            "workflow_type": workflow_type,
            "valid": False,
            "message": f"配置验证失败: {str(e)}",
            "config": None,
        }
