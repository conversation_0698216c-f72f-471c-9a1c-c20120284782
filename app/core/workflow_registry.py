import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Set

import yaml
from fastapi import HTTPException

from app.schemas.common import WorkflowConfig, WorkflowsConfig

logger = logging.getLogger(__name__)


class WorkflowRegistry:
    """
    工作流注册中心

    负责管理和维护所有工作流的配置信息，提供统一的工作流访问接口。
    """

    def __init__(self, config_path: Optional[str] = None):
        """初始化工作流注册中心

        Args:
            config_path: 配置文件路径，默认为 config/workflows.yaml
        """
        self._config_path = config_path or "config/workflows.yaml"
        self._workflows_config: Optional[WorkflowsConfig] = None
        self._load_config()

    def _load_config(self) -> None:
        """加载工作流配置文件"""
        try:
            config_file = Path(self._config_path)
            if not config_file.exists():
                logger.warning(f"工作流配置文件不存在: {self._config_path}")
                # 创建默认配置
                self._workflows_config = WorkflowsConfig(
                    dify={
                        "api_url": os.getenv("DIFY_API_URL", "http://localhost"),
                        "user": os.getenv("DIFY_USER", "admin"),
                    },
                    workflows={},
                )
                return

            with open(config_file, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)

            self._workflows_config = WorkflowsConfig(**config_data)
            logger.info(
                f"成功加载工作流配置，共 {len(self._workflows_config.workflows)} 个工作流"
            )

        except Exception as e:
            logger.error(f"加载工作流配置失败: {str(e)}")
            raise ValueError(f"工作流配置加载失败: {str(e)}")

    def reload_config(self) -> None:
        """重新加载配置文件"""
        logger.info("重新加载工作流配置")
        self._load_config()

    def get_workflow_config(self, workflow_type: str) -> WorkflowConfig:
        """获取指定工作流的配置

        Args:
            workflow_type: 工作流类型

        Returns:
            WorkflowConfig: 工作流配置

        Raises:
            HTTPException: 工作流不存在或未启用
        """
        if not self._workflows_config:
            raise HTTPException(status_code=500, detail="工作流配置未加载")

        workflow_config = self._workflows_config.get_workflow(workflow_type)
        if not workflow_config:
            raise HTTPException(
                status_code=404, detail=f"工作流类型 '{workflow_type}' 不存在"
            )

        if not workflow_config.enabled:
            raise HTTPException(
                status_code=403, detail=f"工作流 '{workflow_type}' 已禁用"
            )

        return workflow_config

    def get_dify_config(self) -> Dict[str, str]:
        """获取Dify平台配置

        Returns:
            Dict[str, str]: 包含api_url和user的配置字典
        """
        if not self._workflows_config:
            raise HTTPException(status_code=500, detail="工作流配置未加载")

        return {
            "api_url": self._workflows_config.dify.api_url,
            "user": self._workflows_config.dify.user,
        }

    def get_workflow_full_config(self, workflow_type: str) -> Dict[str, str]:
        """获取工作流的完整配置（包含Dify平台配置）

        Args:
            workflow_type: 工作流类型

        Returns:
            Dict[str, str]: 完整的工作流配置
        """
        workflow_config = self.get_workflow_config(workflow_type)
        dify_config = self.get_dify_config()

        return {
            "api_key": workflow_config.api_key,
            "api_url": dify_config["api_url"],
            "user": dify_config["user"],
            "timeout": str(workflow_config.timeout),
            "max_retries": str(workflow_config.max_retries),
        }

    def list_workflows(self, enabled_only: bool = True) -> Dict[str, WorkflowConfig]:
        """列出所有工作流

        Args:
            enabled_only: 是否只返回启用的工作流

        Returns:
            Dict[str, WorkflowConfig]: 工作流配置字典
        """
        if not self._workflows_config:
            return {}

        if enabled_only:
            return self._workflows_config.get_enabled_workflows()
        else:
            return self._workflows_config.workflows

    def get_workflow_types(self, enabled_only: bool = True) -> List[str]:
        """获取所有工作流类型列表

        Args:
            enabled_only: 是否只返回启用的工作流类型

        Returns:
            List[str]: 工作流类型列表
        """
        workflows = self.list_workflows(enabled_only)
        return list(workflows.keys())

    def get_workflow_by_identifier(self, identifier: str) -> Optional[WorkflowConfig]:
        """根据唯一标识符获取工作流

        Args:
            identifier: 工作流唯一标识符

        Returns:
            Optional[WorkflowConfig]: 匹配的工作流配置
        """
        if not self._workflows_config:
            return None

        return self._workflows_config.get_workflow_by_identifier(identifier)

    def get_workflow_by_key(self, key: str) -> Optional[WorkflowConfig]:
        """根据工作流key获取配置

        Args:
            key: 工作流key（类型名）

        Returns:
            Optional[WorkflowConfig]: 工作流配置
        """
        if not self._workflows_config:
            return None

        return self._workflows_config.get_workflow_by_key(key)

    def validate_workflow_type(self, workflow_type: str) -> bool:
        """验证工作流类型是否存在且启用

        Args:
            workflow_type: 工作流类型

        Returns:
            bool: 是否有效
        """
        try:
            self.get_workflow_config(workflow_type)
            return True
        except HTTPException:
            return False

    def get_workflow_info(self, workflow_type: str) -> Dict[str, any]:
        """获取工作流的详细信息

        Args:
            workflow_type: 工作流类型

        Returns:
            Dict[str, any]: 工作流详细信息
        """
        workflow_config = self.get_workflow_config(workflow_type)

        return {
            "type": workflow_type,
            "name": workflow_config.name,
            "description": workflow_config.description,
            "enabled": workflow_config.enabled,
            "timeout": workflow_config.timeout,
            "max_retries": workflow_config.max_retries,
            "identifier": workflow_config.identifier,
        }


# 全局工作流注册中心实例
workflow_registry = WorkflowRegistry()
