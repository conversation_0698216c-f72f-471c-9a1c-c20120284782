import logging
from typing import Optional

from fastapi import HTTPException

from app.core.workflow_registry import workflow_registry
from app.enums.workflow_types import WorkflowType
from app.services.dify_service import DifyService
from app.services.sop_service import SOPService

logger = logging.getLogger(__name__)


def get_dify_service_by_type(workflow_type: str) -> DifyService:
    """根据工作流类型获取DifyService实例

    Args:
        workflow_type: 工作流类型

    Returns:
        DifyService: Dify服务实例

    Raises:
        HTTPException: 工作流类型不存在或未启用
    """
    # 验证工作流类型
    if not WorkflowType.validate_workflow_type(workflow_type):
        available_types = WorkflowType.get_enabled_types()
        raise HTTPException(
            status_code=400,
            detail=f"不支持的工作流类型: {workflow_type}。可用类型: {', '.join(available_types)}",
        )

    return DifyService(workflow_type)


def get_sop_service_by_file_type(file_type: int) -> SOPService:
    """根据file_type获取对应的SOP服务实例

    Args:
        file_type: 文件类型 (1: 产线计划, 2: 控制计划)

    Returns:
        SOPService: SOP服务实例

    Raises:
        HTTPException: 文件类型不支持或对应的工作流未启用
    """
    try:
        workflow_type = WorkflowType.get_sop_workflow_by_file_type(file_type)
        dify_service = get_dify_service_by_type(workflow_type)
        return SOPService(dify_service)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


def get_workflow_info(workflow_type: str) -> dict:
    """获取工作流详细信息

    Args:
        workflow_type: 工作流类型

    Returns:
        dict: 工作流详细信息

    Raises:
        HTTPException: 工作流类型不存在
    """
    try:
        return workflow_registry.get_workflow_info(workflow_type)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工作流信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取工作流信息失败: {str(e)}")


def list_available_workflows(enabled_only: bool = True) -> dict:
    """列出所有可用的工作流

    Args:
        enabled_only: 是否只返回启用的工作流

    Returns:
        dict: 工作流列表和详细信息
    """
    try:
        workflows = workflow_registry.list_workflows(enabled_only)
        return {
            "total": len(workflows),
            "workflows": {
                workflow_type: {
                    "name": config.name,
                    "description": config.description,
                    "enabled": config.enabled,
                    "tags": config.tags,
                }
                for workflow_type, config in workflows.items()
            },
        }
    except Exception as e:
        logger.error(f"列出工作流失败: {str(e)}")
        # 返回基本的工作流类型信息
        workflow_types = (
            WorkflowType.get_enabled_types()
            if enabled_only
            else WorkflowType.get_all_types()
        )
        return {
            "total": len(workflow_types),
            "workflows": {
                wt: {"name": wt, "description": None, "enabled": True, "tags": []}
                for wt in workflow_types
            },
        }
