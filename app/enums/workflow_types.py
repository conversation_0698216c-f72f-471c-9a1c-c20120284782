from typing import List

from app.core.workflow_registry import workflow_registry


class WorkflowType:
    """
    工作流类型管理类

    完全基于配置文件管理工作流类型，不再使用硬编码枚举
    所有工作流类型都通过 config/workflows.yaml 配置文件定义
    """

    @classmethod
    def get_all_types(cls) -> List[str]:
        """获取所有工作流类型"""
        return workflow_registry.get_workflow_types(enabled_only=False)

    @classmethod
    def get_enabled_types(cls) -> List[str]:
        """获取所有启用的工作流类型"""
        return workflow_registry.get_workflow_types(enabled_only=True)

    @classmethod
    def get_sop_workflow_by_file_type(cls, file_type: int) -> str:
        """根据file_type获取对应的SOP工作流类型"""
        if file_type == 1:
            return "sop_type1"
        elif file_type == 2:
            return "sop_type2"
        else:
            raise ValueError(f"不支持的file_type: {file_type}，只支持1和2")

    @classmethod
    def validate_workflow_type(cls, workflow_type: str) -> bool:
        """验证工作流类型是否存在且可用"""
        return workflow_registry.validate_workflow_type(workflow_type)

    @classmethod
    def get_workflow_by_identifier(cls, identifier: str) -> str:
        """根据唯一标识符获取工作流类型"""
        config = workflow_registry.get_workflow_by_identifier(identifier)
        if not config:
            raise ValueError(f"未找到标识符为 '{identifier}' 的工作流")

        # 返回对应的工作流key
        for key, workflow_config in workflow_registry.list_workflows(
            enabled_only=False
        ).items():
            if workflow_config.identifier == identifier:
                return key

        raise ValueError(f"未找到标识符为 '{identifier}' 的工作流")

    @classmethod
    def get_workflow_by_key(cls, key: str) -> str:
        """根据工作流key获取工作流类型（直接返回key本身，用于一致性）"""
        if not cls.validate_workflow_type(key):
            raise ValueError(f"工作流类型 '{key}' 不存在或未启用")
        return key

    @classmethod
    def get_workflow_info(cls, workflow_type: str) -> dict:
        """获取工作流详细信息"""
        return workflow_registry.get_workflow_info(workflow_type)

    @classmethod
    def is_valid_type(cls, workflow_type: str) -> bool:
        """验证工作流类型是否有效"""
        return workflow_type in cls.get_all_types()

    @classmethod
    def is_enabled_type(cls, workflow_type: str) -> bool:
        """验证工作流类型是否启用"""
        return workflow_type in cls.get_enabled_types()
