from typing import Any, Dict

from fastapi import HTTPException, UploadFile

from app.services.dify_service import DifyService
from app.utils.clean_json import clean_json_data


class RecruitService:
    """招聘服务类"""

    def __init__(self, dify_service: DifyService):
        self.dify_service = dify_service

    def _validate_file(self, file: UploadFile) -> None:
        """验证上传的文件"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")

        allowed_extensions = {".pdf", ".docx", ".doc"}
        file_extension = file.filename.lower().split(".")[-1]
        if f".{file_extension}" not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件格式。支持的格式: {', '.join(allowed_extensions)}",
            )

    async def process_upload(self, file: UploadFile) -> Dict[str, Any]:
        self._validate_file(file)

        file_bytes = await file.read()
        if not file_bytes:
            raise HTTPException(status_code=400, detail="文件内容为空")

        upload_result = await self.dify_service.upload_file(file.filename, file_bytes)
        if not upload_result:
            raise HTTPException(status_code=500, detail="文件上传失败")

        workflow_result = await self.dify_service.run_workflow({
            "planning_file": {
                "type": "document",
                "transfer_method": "local_file",
                "upload_file_id": upload_result.file_id,
            }
        })
        if not workflow_result:
            raise HTTPException(status_code=500, detail="工作流处理失败")

        if workflow_result.status != 1:
            raise HTTPException(
                status_code=500, detail=f"工作流处理失败: {workflow_result.message}"
            )

        data = clean_json_data(workflow_result.data)

        return data
