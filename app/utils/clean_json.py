keys_to_check = ["required", "preferred", "excluded"]


def is_field_completely_empty(field_data):
    return all(not field_data.get(key, []) for key in keys_to_check)


def clean_json_data(data):
    cleaned_data = {}
    for category, fields in data.items():
        if not isinstance(fields, dict):
            cleaned_data[category] = fields
            continue
        cleaned_fields = {}
        for field_name, field_data in fields.items():
            if isinstance(field_data, dict) and all(
                k in field_data for k in keys_to_check
            ):
                if not is_field_completely_empty(field_data):
                    cleaned_fields[field_name] = field_data
            else:
                cleaned_fields[field_name] = field_data
            cleaned_fields[field_name] = field_data
        cleaned_data[category] = cleaned_fields

    return cleaned_data
