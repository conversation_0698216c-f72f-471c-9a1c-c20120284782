import json
import re


class JSONParser:
    def __init__(self):
        self.patterns = [
            # markdown代码块
            r"```(?:json)?\s*(\{.*?\}|\[.*?\])\s*```",
            # 裸露的JSON对象
            r"(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})",
            # 裸露的JSON数组
            r"(\[[^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*\])",
        ]

        # 思考块的正则表达式模式
        self.thinking_patterns = [
            # 标准思考块格式
            r'<details\s+style="[^"]*"\s+open>\s*<summary>\s*Thinking\.\.\.\s*</summary>\s*.*?</details>\s*',
            # 简化的思考块格式
            r"<details[^>]*>\s*<summary>\s*Thinking\.\.\.\s*</summary>\s*.*?</details>\s*",
            # 更通用的思考块格式
            r"<details[^>]*>\s*<summary>[^<]*(?:thinking|思考)[^<]*</summary>\s*.*?</details>\s*",
        ]

    def remove_thinking_blocks(self, text):
        """移除文本中的思考块"""
        cleaned_text = text

        for pattern in self.thinking_patterns:
            # 使用 DOTALL 标志使 . 匹配换行符，IGNORECASE 忽略大小写
            cleaned_text = re.sub(
                pattern, "", cleaned_text, flags=re.DOTALL | re.IGNORECASE
            )

        return cleaned_text.strip()

    def extract_json_with_braces(self, text):
        """使用括号计数提取完整的JSON对象"""
        results = []
        brace_count = 0
        start_idx = -1

        for i, char in enumerate(text):
            if char == "{":
                if start_idx == -1:
                    start_idx = i
                brace_count += 1
            elif char == "}":
                brace_count -= 1
                if brace_count == 0 and start_idx != -1:
                    json_str = text[start_idx : i + 1]
                    try:
                        parsed = json.loads(json_str)
                        results.append(parsed)
                    except json.JSONDecodeError:
                        pass
                    start_idx = -1

        # 返回最大的JSON对象（通常是完整的数据）
        if results:
            return max(results, key=lambda x: len(json.dumps(x)))
        return None

    def extract_json_with_brackets(self, text):
        """使用括号计数提取完整的JSON数组"""
        results = []
        bracket_count = 0
        start_idx = -1

        for i, char in enumerate(text):
            if char == "[":
                if start_idx == -1:
                    start_idx = i
                bracket_count += 1
            elif char == "]":
                bracket_count -= 1
                if bracket_count == 0 and start_idx != -1:
                    json_str = text[start_idx : i + 1]
                    try:
                        parsed = json.loads(json_str)
                        results.append(parsed)
                    except json.JSONDecodeError:
                        pass
                    start_idx = -1

        # 返回最大的JSON数组（通常是完整的数据）
        if results:
            return max(results, key=lambda x: len(json.dumps(x)))
        return None

    def find_complete_json_object(self, text):
        """寻找完整的JSON对象，改进的括号匹配算法"""
        results = []
        i = 0

        while i < len(text):
            if text[i] == "{":
                # 找到JSON对象的开始
                start_idx = i
                brace_count = 1
                i += 1
                in_string = False
                escape_next = False

                while i < len(text) and brace_count > 0:
                    char = text[i]

                    if escape_next:
                        escape_next = False
                    elif char == "\\":
                        escape_next = True
                    elif char == '"' and not escape_next:
                        in_string = not in_string
                    elif not in_string:
                        if char == "{":
                            brace_count += 1
                        elif char == "}":
                            brace_count -= 1

                    i += 1

                if brace_count == 0:
                    # 找到完整的JSON对象
                    json_str = text[start_idx:i]
                    try:
                        parsed = json.loads(json_str)
                        results.append(parsed)
                    except json.JSONDecodeError:
                        pass
            else:
                i += 1

        # 返回最大的JSON对象
        if results:
            return max(results, key=lambda x: len(json.dumps(x)))
        return None

    def find_complete_json_array(self, text):
        """寻找完整的JSON数组，改进的括号匹配算法"""
        results = []
        i = 0

        while i < len(text):
            if text[i] == "[":
                # 找到JSON数组的开始
                start_idx = i
                bracket_count = 1
                i += 1
                in_string = False
                escape_next = False

                while i < len(text) and bracket_count > 0:
                    char = text[i]

                    if escape_next:
                        escape_next = False
                    elif char == "\\":
                        escape_next = True
                    elif char == '"' and not escape_next:
                        in_string = not in_string
                    elif not in_string:
                        if char == "[":
                            bracket_count += 1
                        elif char == "]":
                            bracket_count -= 1

                    i += 1

                if bracket_count == 0:
                    # 找到完整的JSON数组
                    json_str = text[start_idx:i]
                    try:
                        parsed = json.loads(json_str)
                        results.append(parsed)
                    except json.JSONDecodeError:
                        pass
            else:
                i += 1

        # 返回最大的JSON数组
        if results:
            return max(results, key=lambda x: len(json.dumps(x)))
        return None

    def clean_text_for_json(self, text):
        """清理文本以便更好地解析JSON"""
        # 移除思考块
        cleaned = self.remove_thinking_blocks(text)

        # 移除日志前缀和时间戳
        cleaned = re.sub(
            r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} - [^-]+ - \[[^\]]+\] - [^-]+ - [^:]+:",
            "",
            cleaned,
            flags=re.MULTILINE,
        )

        # 移除其他可能的日志信息
        cleaned = re.sub(r"Dify:\s*", "", cleaned)

        # 移除多余的空白字符
        cleaned = re.sub(r"\s+", " ", cleaned)

        return cleaned.strip()

    def parse(self, text):
        """解析文本中的JSON - 优化版本"""
        if not text or not isinstance(text, str):
            raise ValueError("输入必须是非空字符串")

        # 步骤1：清理文本
        cleaned_text = self.clean_text_for_json(text)

        # 步骤2：直接解析整个文本
        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            pass

        # 步骤3：移除可能的markdown标记后再次尝试
        markdown_cleaned = re.sub(
            r"^```(?:json)?\s*", "", cleaned_text, flags=re.IGNORECASE | re.MULTILINE
        )
        markdown_cleaned = re.sub(r"\s*```$", "", markdown_cleaned, flags=re.MULTILINE)

        try:
            return json.loads(markdown_cleaned.strip())
        except json.JSONDecodeError:
            pass

        # 步骤4：使用改进的括号匹配算法寻找完整的JSON对象
        json_obj = self.find_complete_json_object(cleaned_text)
        if json_obj is not None:
            return json_obj

        # 步骤5：使用改进的括号匹配算法寻找完整的JSON数组
        json_arr = self.find_complete_json_array(cleaned_text)
        if json_arr is not None:
            return json_arr

        # 步骤6：使用原有的正则表达式方法作为备选
        all_matches = []
        for pattern in self.patterns:
            matches = re.findall(pattern, cleaned_text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    parsed = json.loads(match.strip())
                    all_matches.append(parsed)
                except json.JSONDecodeError:
                    continue

        # 返回最大的匹配结果
        if all_matches:
            return max(all_matches, key=lambda x: len(json.dumps(x)))

        # 步骤7：使用原有的括号计数方法作为最后备选
        json_obj = self.extract_json_with_braces(cleaned_text)
        if json_obj is not None:
            return json_obj

        json_arr = self.extract_json_with_brackets(cleaned_text)
        if json_arr is not None:
            return json_arr

        raise ValueError(
            f"无法从文本中提取有效的JSON。清理后的文本前200字符: {cleaned_text[:200]}..."
        )
