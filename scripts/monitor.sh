#!/bin/bash

# FastAPI应用监控脚本
# 使用方法: ./monitor.sh [container_name] [health_url]

CONTAINER_NAME=${1:-"ai_server"}
HEALTH_URL=${2:-"http://localhost:30101/health"}
LOG_FILE="/var/log/ai_server_monitor.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# 检查容器状态
check_container() {
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        return 0
    else
        return 1
    fi
}

# 检查健康状态
check_health() {
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$HEALTH_URL" 2>/dev/null)
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        local status=$(cat /tmp/health_response.json 2>/dev/null | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        if [ "$status" = "healthy" ]; then
            return 0
        elif [ "$status" = "shutting_down" ]; then
            return 2  # 正在关闭
        else
            return 1  # 不健康
        fi
    else
        return 1  # HTTP错误
    fi
}

# 获取容器资源使用情况
get_container_stats() {
    if check_container; then
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" $CONTAINER_NAME
    fi
}

# 主监控循环
monitor() {
    log "开始监控容器: $CONTAINER_NAME"
    
    while true; do
        if check_container; then
            check_health
            health_status=$?
            
            case $health_status in
                0)
                    log "✅ 服务健康 - 容器运行正常"
                    ;;
                1)
                    log "❌ 服务不健康 - 健康检查失败"
                    # 可以在这里添加告警逻辑
                    ;;
                2)
                    log "⚠️  服务正在关闭"
                    ;;
            esac
            
            # 每5分钟记录一次资源使用情况
            if [ $(($(date +%s) % 300)) -eq 0 ]; then
                log "资源使用情况:"
                get_container_stats | tee -a $LOG_FILE
            fi
            
        else
            log "❌ 容器未运行"
            # 可以在这里添加自动重启逻辑
        fi
        
        sleep 30
    done
}

# 显示帮助信息
show_help() {
    echo "FastAPI应用监控脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项] [容器名] [健康检查URL]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -s, --status   检查当前状态"
    echo "  -m, --monitor  开始监控（默认）"
    echo "  -l, --logs     显示监控日志"
    echo ""
    echo "示例:"
    echo "  $0 ai_server http://localhost:30101/health"
    echo "  $0 --status"
    echo "  $0 --logs"
}

# 显示当前状态
show_status() {
    echo "=== FastAPI应用状态 ==="
    echo "容器名称: $CONTAINER_NAME"
    echo "健康检查URL: $HEALTH_URL"
    echo ""
    
    if check_container; then
        echo "容器状态: ✅ 运行中"
        
        check_health
        case $? in
            0) echo "健康状态: ✅ 健康" ;;
            1) echo "健康状态: ❌ 不健康" ;;
            2) echo "健康状态: ⚠️  正在关闭" ;;
        esac
        
        echo ""
        echo "资源使用情况:"
        get_container_stats
    else
        echo "容器状态: ❌ 未运行"
    fi
}

# 显示日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -n 50 "$LOG_FILE"
    else
        echo "监控日志文件不存在: $LOG_FILE"
    fi
}

# 主程序
case "${1:-}" in
    -h|--help)
        show_help
        ;;
    -s|--status)
        show_status
        ;;
    -l|--logs)
        show_logs
        ;;
    -m|--monitor|"")
        monitor
        ;;
    *)
        if [[ "$1" =~ ^- ]]; then
            echo "未知选项: $1"
            show_help
            exit 1
        else
            monitor
        fi
        ;;
esac
